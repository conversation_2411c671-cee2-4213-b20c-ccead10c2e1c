import { PrismaClient } from "@prisma/client";

export async function seedZones(prisma: PrismaClient) {
  const zones = await prisma.deliveryZone.create({
    data: {
      name: "Mollah<PERSON>",
      slug: "mollahat",
      nam: "মোল্লাহাট",

      subZones: {
        create: [
          {
            name: "Udoypur",
            slug: "udoypur",
            nam: "উদয়পুর",
            isBase: true,

            subZones: {
              createMany: {
                data: [
                  {
                    name: "Aruakandi",
                    slug: "aruakandi",
                    nam: "আড়ুয়াকান্দি",
                    charge: 10,
                    express: 20,
                    isBase: true,
                  },
                  {
                    name: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
                    slug: "doybokandi",
                    nam: "দৈবকান্দি",
                    charge: 10,
                    express: 20,
                    isBase: true,
                  },
                  {
                    name: "Uttorkandi",
                    slug: "uttorkandi",
                    nam: "উত্তরকান্দি",
                    charge: 15,
                    express: 30,
                    isBase: true,
                  },
                  {
                    name: "Golar<PERSON><PERSON>",
                    slug: "golarchok",
                    nam: "গোলারচক",
                    charge: 10,
                    express: 20,
                    isBase: true,
                  },
                  {
                    name: "Sorda<PERSON><PERSON>",
                    slug: "sordarpara",
                    nam: "সরদারপাড়া",
                    charge: 10,
                    express: 20,
                    isBase: true,
                  },
                  {
                    name: "Masterpar<PERSON>",
                    slug: "masterpara",
                    nam: "মাস্টারপাড়া",
                    charge: 10,
                    express: 20,
                    isBase: true,
                  },
                ],
              },
            },
          },
          {
            name: "Kahalpur",
            slug: "kahalpur",
            nam: "কাহালপুর",

            subZones: {
              createMany: {
                data: [
                  {
                    name: "Kahalpur Purbopara",
                    slug: "kahalpur-purbopara",
                    nam: "কাহালপুর পূর্বপাড়া",
                    charge: 15,
                    express: 30,
                    isBase: true,
                  },
                  {
                    name: "Kahalpur moddhopara",
                    slug: "kahalpur-moddhopara",
                    nam: "কাহালপুর মধ্যপাড়া",
                    charge: 20,
                    express: 40,
                    isBase: true,
                  },
                  {
                    name: "Kahalpur Poschimpara",
                    slug: "kahalpur-poschimpara",
                    nam: "কাহালপুর পশ্চিমপাড়া",
                    charge: 25,
                    express: 50,
                    isBase: true,
                  },
                ],
              },
            },
          },
          {
            name: "astail",
            slug: "astail",
            nam: "আস্তাইল",

            subZones: {
              createMany: {
                data: [
                  {
                    name: "Chor Astail",
                    slug: "chor-astail",
                    nam: "চর-আস্তাইল",
                    charge: 25,
                    express: 50,
                    isBase: true,
                  },
                  {
                    name: "Astail Kha Bari",
                    slug: "astail-kha-bari",
                    nam: "আস্তাইল খা-পাড়া",
                    charge: 20,
                    express: 40,
                    isBase: true,
                  },
                  {
                    name: "Astail Gram",
                    slug: "astail-gram",
                    nam: "আস্তাইল গ্রাম",
                    charge: 15,
                    express: 30,
                    isBase: true,
                  },
                  {
                    name: "Moupura",
                    slug: "moupura",
                    nam: "মৌপুরা",
                    charge: 25,
                    express: 50,
                    isBase: true,
                  },
                ],
              },
            },
          },
          {
            name: "Garfa",
            slug: "garfa",
            nam: "গারফা",
            charge: 10,
            express: 20,
            isBase: true,
          },
          {
            name: "Boalia",
            slug: "boalia",
            nam: "বোয়ালিয়া",
            charge: 10,
            express: 20,
            isBase: true,
          },
          {
            name: "Meherpur",
            slug: "meherpur",
            nam: "মেহেরপুর",
            charge: 15,
            express: 30,
            isBase: true,
          },
          {
            name: "Golargati",
            slug: "golargati",
            nam: "গোলারগাতি",
            charge: 20,
            express: 40,
            isBase: true,
          },
          {
            name: "Ghoradair",
            slug: "ghoradair",
            nam: "ঘোড়াদাইড়",
            charge: 20,
            express: 40,
            isBase: true,
          },
          {
            name: "Mollarkul",
            slug: "mollarkul",
            nam: "মোল্লারকুল",
            charge: 25,
            express: 50,
            isBase: true,
          },
          {
            name: "Vandar Khola",
            slug: "vandar-khola",
            nam: "ভান্ডারখোলা",
            charge: 20,
            express: 40,
            isBase: true,
          },
          {
            name: "Pickup Point",
            slug: "pickup-point",
            nam: "পিকআপ পয়েন্ট",
            charge: 0,
            express: 0,
            isBase: true,
          },
        ],
      },
    },
  });

  return zones;
}

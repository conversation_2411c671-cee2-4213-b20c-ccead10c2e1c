import { Metadata, ResolvingMetadata } from "next";
import { getPrisma } from "@udoy/utils/db-utils";
import { PageProps } from "@udoy/utils/types";
import CategoryItem from "../../components/CategoryItem";
import Icon from "@udoy/components/Icon";
import Hide from "@udoy/components/Hide";
import { Category } from "@prisma/client";
import Link from "next/link";
import ProductItem from "../../components/ProductItem";
import { getLocale } from "next-intl/server";
import SyncActivePath from "../../components/SyncActivePath";
import {
  unstable_cacheLife as cacheLife,
  unstable_cacheTag as cacheTag,
} from "next/cache";
import { CacheKey } from "@udoy/utils/cache-key";
import { HomeIcon } from "lucide-react";
import { Env } from "@udoy/libs/env";
import { flattenPathToParent } from "../utils";

// Define the type for the params
type Props = {
  params: Promise<{ slug: string; locale: string }>;
};

// Generate metadata for the page
export async function generateMetadata(
  { params }: Props,
  parent: ResolvingMetadata
): Promise<Metadata> {
  const { slug, locale } = await params;
  const isBangla = locale === "bn";

  // Fetch category data
  const category = await getPrisma().category.findUnique({
    where: { slug },
  });

  if (!category) {
    return {
      title: "Category Not Found",
      description: "The requested category could not be found.",
    };
  }

  // Get category title in appropriate language
  const categoryTitle = isBangla
    ? category.titleBn || category.nam || category.name
    : category.titleEn || category.name;

  // Get category description in appropriate language
  const description = isBangla
    ? category.descriptionBn ||
      `${
        category.nam || category.name
      } - উদয় মার্টে সেরা মানের পণ্য সরবরাহ করা হয়।`
    : category.descriptionEn ||
      `${category.name} - Shop quality products at UdoyMart.`;

  // Get category image or use a default
  const imageUrl = category.image || "/category-placeholder.png";

  return {
    title: categoryTitle,
    description: description,
    openGraph: {
      title: `${categoryTitle} | ${isBangla ? "উদয় মার্ট" : "UdoyMart"}`,
      description: description,
      images: [
        {
          url: imageUrl,
          width: 800,
          height: 600,
          alt: categoryTitle,
        },
      ],
      locale: isBangla ? "bn_BD" : "en_US",
      type: "website",
      siteName: isBangla ? "উদয় মার্ট" : "UdoyMart",
      url: `${Env.NEXT_PUBLIC_FRONTEND_URL}/${locale}/${slug}`,
    },
    twitter: {
      card: "summary_large_image",
      title: `${categoryTitle} | ${isBangla ? "উদয় মার্ট" : "UdoyMart"}`,
      description: description,
      images: [imageUrl],
    },
  };
}

async function getData(parentSlug?: string) {
  "use cache";
  const start = Date.now();
  if (!parentSlug) {
    return {
      categories: [],
      path: [],
      category: null,
    };
  }

  cacheLife("weeks");
  cacheTag(CacheKey.ShopPage(parentSlug));
  cacheTag(CacheKey.ShopPage());

  const category = await getPrisma().category.findUnique({
    where: { slug: parentSlug },
    include: {
      products: {
        where: {
          hide: false,
        },
        include: {
          images: true,
          unit: true,
          category: { select: { slug: true } },
          availability: true,
        },
        orderBy: [
          {
            featured: "desc",
          },
          {
            position: "asc",
          },
        ],
      },
    },
  });

  const pathToParent = await getPrisma().category.findUnique({
    where: { slug: parentSlug },
    select: {
      slug: true,
      nam: true,
      parentCategory: {
        select: {
          slug: true,
          nam: true,
          parentCategory: {
            select: { slug: true, nam: true, parentCategory: true },
          },
        },
      },
    },
  });
  const path = flattenPathToParent(pathToParent as any).reverse();

  if (category?.isBase) {
    console.log("Time taken", Date.now() - start);
    return {
      categories: [],
      path,
      category,
    };
  }

  const categories = await getPrisma().category.findMany({
    where: { parentCategory: { slug: parentSlug }, hide: false },
    orderBy: [
      {
        featured: "desc",
      },
      {
        position: "asc",
      },
    ],
  });

  console.log("Time taken", Date.now() - start);

  return {
    categories,
    path,
    category,
  };
}

async function CategoriesPage(props: PageProps<{ slug: string }>) {
  const params = await props.params;
  const locale = await getLocale();
  const isBngla = locale === "bn";

  const { categories, path, category } = await getData(params.slug);
  return (
    <main
      className="flex-1 overflow-y-scroll pb-6 relative"
      style={{ height: "calc(100vh - 64px)" }}
    >
      <div className="flex gap-1 items-center bg-background z-40 sticky top-0 duration-200 px-4 md:px-8 py-4">
        <Link href={{ pathname: "/" }} className="">
          <HomeIcon className="h-4 w-4 text-muted-foreground" />
        </Link>

        <Icon icon="chevron-right" className="text-sm text-muted-foreground" />
        {path.map((category, i) => (
          <div key={category.en} className="flex items-center">
            <Link
              className="font-semibold text-sm text-muted-foreground truncate"
              href={{ pathname: `/${category.en}` }}
              style={{ maxWidth: Math.floor(90 / path.length) + "vw" }}
            >
              {isBngla ? category.bn : category.en}
            </Link>
            <Hide open={i < path.length - 1}>
              <Icon
                icon="chevron-right"
                className="text-sm ml-1 text-muted-foreground"
              />
            </Hide>
          </div>
        ))}
      </div>
      <SyncActivePath path={path.map((item) => item.en)} />
      <div className="px-4 md:px-8">
        <Hide
          open={category?.isBase}
          fallback={
            <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
              {categories.map((category) => (
                <CategoryItem key={category.id} category={category} />
              ))}
            </div>
          }
        >
          <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
            {category?.products?.map((product) => (
              <ProductItem key={product.id} product={product} locale={locale} />
            ))}
          </div>
        </Hide>
      </div>
    </main>
  );
}

export default CategoriesPage;

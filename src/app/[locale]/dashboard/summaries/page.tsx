"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@udoy/components/ui/button";
import { Card, CardContent, CardHeader } from "@udoy/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@udoy/components/ui/table";
import { Input } from "@udoy/components/ui/input";
import { Badge } from "@udoy/components/ui/badge";
import { Plus, Search, Eye } from "lucide-react";
import Link from "next/link";
import { SummaryType } from "@prisma/client";
import { getSummaries, type SummaryWithCreator } from "./actions";
import { SummaryCreationModal } from "./components/SummaryCreationMOdal";
import { toast } from "sonner";

export default function SummariesPage() {
  const [summaries, setSummaries] = useState<SummaryWithCreator[]>([]);
  const [filteredSummaries, setFilteredSummaries] = useState<
    SummaryWithCreator[]
  >([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

  useEffect(() => {
    loadSummaries();
  }, []);

  useEffect(() => {
    const filtered = summaries.filter(
      (summary) =>
        summary.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
        summary.generatedBy.name
          .toLowerCase()
          .includes(searchTerm.toLowerCase())
    );
    setFilteredSummaries(filtered);
  }, [summaries, searchTerm]);

  const loadSummaries = async () => {
    try {
      setLoading(true);
      const data = await getSummaries();
      setSummaries(data);
    } catch (error) {
      console.error("Failed to load summaries:", error);
      toast.error("Failed to load summaries. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const formatDateRange = (summary: SummaryWithCreator) => {
    const start = new Date(summary.startDate);
    const end = new Date(summary.endDate);

    if (summary.type === SummaryType.DAILY) {
      return start.toLocaleDateString("en-US", {
        month: "long",
        day: "numeric",
        year: "numeric",
      });
    }

    if (summary.type === SummaryType.WEEKLY) {
      return `${start.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
      })} - ${end.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
        year: "numeric",
      })}`;
    }

    if (summary.type === SummaryType.MONTHLY) {
      return start.toLocaleDateString("en-US", {
        month: "long",
        year: "numeric",
      });
    }

    return start.getFullYear().toString();
  };

  const getSummaryTypeColor = (type: SummaryType) => {
    switch (type) {
      case SummaryType.DAILY:
        return "bg-blue-100 text-blue-800";
      case SummaryType.WEEKLY:
        return "bg-green-100 text-green-800";
      case SummaryType.MONTHLY:
        return "bg-purple-100 text-purple-800";
      case SummaryType.YEARLY:
        return "bg-orange-100 text-orange-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const handleSummaryCreated = () => {
    loadSummaries();
    setIsCreateModalOpen(false);
    toast.success("Summary created successfully!");
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  return (
    <div className=" p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Financial Summaries</h1>
          <p className="text-gray-600 mt-1">
            Manage and view your business summaries
          </p>
        </div>
        <Button
          onClick={() => setIsCreateModalOpen(true)}
          className="flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          Create New Summary
        </Button>
      </div>

      <Card>
        <CardHeader>
          <div className="flex items-center gap-4">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search summaries..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Type</TableHead>
                <TableHead>Date Range</TableHead>
                <TableHead className="text-right">Total Revenue</TableHead>
                <TableHead className="text-right">Total Profit</TableHead>
                <TableHead className="text-right">Total Orders</TableHead>
                <TableHead>Generated By</TableHead>
                <TableHead className="text-center">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredSummaries.map((summary) => (
                <TableRow key={summary.id}>
                  <TableCell>
                    <Badge className={getSummaryTypeColor(summary.type)}>
                      {summary.type}
                    </Badge>
                  </TableCell>
                  <TableCell>{formatDateRange(summary)}</TableCell>
                  <TableCell className="text-right font-medium">
                    ${summary.totalRevenue.toLocaleString()}
                  </TableCell>
                  <TableCell className="text-right font-medium">
                    ${summary.totalProfit.toLocaleString()}
                  </TableCell>
                  <TableCell className="text-right">
                    {summary.totalOrders}
                  </TableCell>
                  <TableCell>{summary.generatedBy.name}</TableCell>
                  <TableCell className="text-center">
                    <Link href={`/dashboard/summaries/${summary.id}`}>
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex items-center gap-1 bg-transparent"
                      >
                        <Eye className="h-3 w-3" />
                        View Details
                      </Button>
                    </Link>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {filteredSummaries.length === 0 && !loading && (
            <div className="text-center py-12 text-gray-500">
              <div className="mb-4">
                <svg
                  className="mx-auto h-12 w-12 text-gray-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  aria-hidden="true"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {searchTerm ? "No summaries found" : "No summaries yet"}
              </h3>
              <p className="text-gray-500 mb-4">
                {searchTerm
                  ? "No summaries match your search criteria."
                  : "Get started by creating your first financial summary."}
              </p>
              {!searchTerm && (
                <Button
                  onClick={() => setIsCreateModalOpen(true)}
                  className="flex items-center gap-2 mx-auto"
                >
                  <Plus className="h-4 w-4" />
                  Create Your First Summary
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      <SummaryCreationModal
        open={isCreateModalOpen}
        onOpenChange={setIsCreateModalOpen}
        onSummaryCreated={handleSummaryCreated}
      />
    </div>
  );
}

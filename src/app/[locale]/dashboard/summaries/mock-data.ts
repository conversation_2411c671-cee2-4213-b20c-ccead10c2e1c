import { Order, OrderItem, OrderStatus, Product, Summary, SummaryType, User } from "@prisma/client"

// Mock users
const mockUsers = [
  { id: 1, name: "<PERSON> Ad<PERSON>", email: "<EMAIL>" },
  { id: 2, name: "<PERSON>", email: "<EMAIL>" },
  { id: 3, name: "<PERSON> Customer", email: "<EMAIL>" },
  { id: 4, name: "<PERSON> Customer", email: "<EMAIL>" },
  { id: 5, name: "Charlie Customer", email: "<EMAIL>" },
] as User[]

// Mock products
const mockProducts = [
  { id: "prod1", name: "Premium Coffee Beans", sourcePrice: 1200 },
  { id: "prod2", name: "Organic Tea Leaves", sourcePrice: 800 },
  { id: "prod3", name: "Fresh Milk", sourcePrice: 300 },
  { id: "prod4", name: "Whole Wheat Bread", sourcePrice: 150 },
  { id: "prod5", name: "Free Range Eggs", sourcePrice: 250 },
  { id: "prod6", name: "Organic Honey", sourcePrice: 600 },
  { id: "prod7", name: "Greek Yogurt", sourcePrice: 400 },
  { id: "prod8", name: "Artisan Cheese", sourcePrice: 900 },
] as Product[]

// Generate mock orders for the last two months
const generateMockOrders = (): Order[] => {
  const orders: Order[] = []
  const now = new Date()
  const twoMonthsAgo = new Date(now.getFullYear(), now.getMonth() - 2, 1)

  for (let i = 1; i <= 400; i++) {
    const randomDate = new Date(twoMonthsAgo.getTime() + Math.random() * (now.getTime() - twoMonthsAgo.getTime()))

    const buyer = mockUsers[Math.floor(Math.random() * (mockUsers.length - 2)) + 2] // Exclude admins
    const numItems = Math.floor(Math.random() * 4) + 1 // 1-4 items per order

    const orderItems: OrderItem[] = []
    let subTotal = 0

    for (let j = 0; j < numItems; j++) {
      const product = mockProducts[Math.floor(Math.random() * mockProducts.length)]
      const quantity = Math.floor(Math.random() * 3) + 1
      const price = product.sourcePrice + Math.floor(Math.random() * 200) + 50 // Markup
      const purchasePrice = product.sourcePrice + Math.floor(Math.random() * 100) - 50 // Slight variation

      const orderItem: OrderItem = {
        id: j + 1,
        productId: product.id,
        product,
        quantity,
        price,
        purchasePrice: Math.max(purchasePrice, product.sourcePrice * 0.8), // Ensure reasonable price
      }

      orderItems.push(orderItem)
      subTotal += price * quantity
    }

    const shipping = Math.floor(Math.random() * 100) + 50
    const discount = Math.floor(Math.random() * subTotal * 0.1)
    const calculatedProfit = orderItems.reduce(
      (acc, item) => acc + (item.price - item.sourcePrice) * item.quantity,
      0,
    )

    const order: Order = {
      id: i,
      buyerId: buyer.id,
      buyer: buyer as any,
      createdAt: randomDate,
      subTotal,
      shipping,
      discount,
      status: Object.values(OrderStatus)[Math.floor(Math.random() * Object.values(OrderStatus).length)],
      profit: calculatedProfit,
      adjustedProfit: null,
      orderItems,
      summaryId: null,
    }

    orders.push(order)
  }

  return orders.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
}

// Generate mock summaries
const generateMockSummaries = (): Summary[] => {
  const summaries: Summary[] = []
  const now = new Date()

  // Generate some daily summaries for recent days
  for (let i = 0; i < 7; i++) {
    const date = new Date(now)
    date.setDate(date.getDate() - i)

    const summary: Summary = {
      id: `summary-daily-${i}`,
      type: SummaryType.DAILY,
      startDate: new Date(date.getFullYear(), date.getMonth(), date.getDate()),
      endDate: new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59),
      totalOrders: Math.floor(Math.random() * 10) + 5,
      totalRevenue: Math.floor(Math.random() * 50000) + 10000,
      totalDeliveryCharges: Math.floor(Math.random() * 2000) + 500,
      totalProfit: Math.floor(Math.random() * 15000) + 3000,
      notes: i === 0 ? "Peak sales day with high customer engagement" : null,
      isFinalized: true,
      generatedById: 1,
      generatedBy: mockUsers[0] as User,
      createdAt: date,
      parentId: null,
      childSummaries: [],
      orders: [],
    }

    summaries.push(summary)
  }

  return summaries
}

const mockOrders = generateMockOrders()
const mockSummaries = generateMockSummaries()

// Mock API functions
export const getSummaries = async (type?: SummaryType): Promise<Summary[]> => {
  await new Promise((resolve) => setTimeout(resolve, 800)) // Simulate network delay

  if (type) {
    return mockSummaries.filter((summary) => summary.type === type)
  }

  return mockSummaries
}

export const getOrdersForDateRange = async (startDate: Date, endDate: Date): Promise<Order[]> => {
  await new Promise((resolve) => setTimeout(resolve, 1000))

  return mockOrders.filter((order) => {
    const orderDate = new Date(order.createdAt)
    return orderDate >= startDate && orderDate <= endDate
  })
}

export const getChildSummariesForDateRange = async (
  type: SummaryType,
  startDate: Date,
  endDate: Date,
): Promise<Summary[]> => {
  await new Promise((resolve) => setTimeout(resolve, 800))

  // For weekly summaries, return daily summaries within the range
  if (type === SummaryType.WEEKLY) {
    return mockSummaries.filter(
      (summary) =>
        summary.type === SummaryType.DAILY &&
        new Date(summary.startDate) >= startDate &&
        new Date(summary.endDate) <= endDate,
    )
  }

  return []
}

export const createSummary = async (data: any): Promise<Summary> => {
  await new Promise((resolve) => setTimeout(resolve, 1200))

  const newSummary: Summary = {
    id: `summary-${Date.now()}`,
    type: data.type,
    startDate: data.startDate,
    endDate: data.endDate,
    totalOrders: data.totalOrders || 0,
    totalRevenue: data.totalRevenue || 0,
    totalDeliveryCharges: data.totalDeliveryCharges || 0,
    totalProfit: data.totalProfit || 0,
    notes: data.notes || null,
    isFinalized: true,
    generatedById: 1,
    generatedBy: mockUsers[0],
    createdAt: new Date(),
    parentId: null,
    childSummaries: [],
    orders: data.orders || [],
  }

  mockSummaries.unshift(newSummary)
  return newSummary
}

export const getSummaryById = async (id: string): Promise<Summary | null> => {
  await new Promise((resolve) => setTimeout(resolve, 600))

  return mockSummaries.find((summary) => summary.id === id) || null
}

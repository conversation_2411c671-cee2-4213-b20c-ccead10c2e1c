"use server";

import { getPrisma } from "@udoy/utils/db-utils";
import { CookieUtil } from "@udoy/utils/cookie-util";
import { Summary, SummaryType, Order, OrderStatus } from "@prisma/client";
import { revalidatePath } from "next/cache";

// Type definitions for return values
export type SummaryWithCreator = Summary & {
  generatedBy: {
    id: number;
    name: string;
    email: string;
  };
};

export type OrderWithDetails = Order & {
  buyer: {
    id: number;
    name: string;
    email: string;
  };
  orderItems: Array<{
    id: number;
    productId: string;
    quantity: number;
    price: number;
    sourcePrice: number;
    product: {
      id: string;
      name: string;
      sourcePrice: number;
    };
  }>;
};

export type SummaryWithDetails = Summary & {
  generatedBy: {
    id: number;
    name: string;
    email: string;
  };
  orders: OrderWithDetails[];
  childSummaries: SummaryWithCreator[];
};

/**
 * Fetch all summaries with creator information
 */
export async function getSummaries(type?: SummaryType): Promise<SummaryWithCreator[]> {
  try {
    const prisma = getPrisma();

    const whereClause = type ? { type } : {};

    const summaries = await prisma.summary.findMany({
      where: whereClause,
      include: {
        generatedBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return summaries;
  } catch (error) {
    console.error('Failed to fetch summaries:', error);
    throw new Error('Failed to fetch summaries');
  }
}

/**
 * Fetch orders within a specific date range for summary creation
 */
export async function getOrdersForDateRange(
  startDate: Date,
  endDate: Date
): Promise<OrderWithDetails[]> {
  try {
    const prisma = getPrisma();

    const orders = await prisma.order.findMany({
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
        status: {
          in: [OrderStatus.DELIVERED, OrderStatus.CONFIRMED, OrderStatus.PACKED, OrderStatus.SHIPPING],
        },
      },
      include: {
        buyer: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        orderItems: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                sourcePrice: true,
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Calculate profit for each order if not already set
    const ordersWithProfit = orders.map(order => {
      if (order.profit === 0) {
        const calculatedProfit = order.orderItems.reduce((total, item) => {
          const itemProfit = (item.price - item.sourcePrice) * item.quantity;
          return total + itemProfit;
        }, 0);
        return { ...order, profit: calculatedProfit };
      }
      return order;
    });

    return ordersWithProfit;
  } catch (error) {
    console.error('Failed to fetch orders for date range:', error);
    throw new Error('Failed to fetch orders for the specified date range');
  }
}

/**
 * Fetch child summaries for a given date range (used for weekly/monthly/yearly summaries)
 */
export async function getChildSummariesForDateRange(
  type: SummaryType,
  startDate: Date,
  endDate: Date
): Promise<SummaryWithCreator[]> {
  try {
    const prisma = getPrisma();

    // Determine the child summary type based on parent type
    let childType: SummaryType;
    switch (type) {
      case SummaryType.WEEKLY:
        childType = SummaryType.DAILY;
        break;
      case SummaryType.MONTHLY:
        childType = SummaryType.WEEKLY;
        break;
      case SummaryType.YEARLY:
        childType = SummaryType.MONTHLY;
        break;
      default:
        return []; // Daily summaries don't have child summaries
    }

    const childSummaries = await prisma.summary.findMany({
      where: {
        type: childType,
        startDate: {
          gte: startDate,
        },
        endDate: {
          lte: endDate,
        },
        isFinalized: true,
      },
      include: {
        generatedBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        startDate: 'asc',
      },
    });

    return childSummaries;
  } catch (error) {
    console.error('Failed to fetch child summaries:', error);
    throw new Error('Failed to fetch child summaries for the specified date range');
  }
}

/**
 * Create a new summary with the provided data
 */
export async function createSummary(data: {
  type: SummaryType;
  startDate: Date;
  endDate: Date;
  totalOrders: number;
  totalRevenue: number;
  totalDeliveryCharges: number;
  totalProfit: number;
  notes?: string | null;
  orderIds?: number[];
}): Promise<SummaryWithCreator> {
  try {
    const userId = await CookieUtil.userId();
    if (!userId) {
      throw new Error('User not authenticated');
    }

    const prisma = getPrisma();

    // Check if a summary already exists for this type and date range
    const existingSummary = await prisma.summary.findFirst({
      where: {
        type: data.type,
        startDate: data.startDate,
        endDate: data.endDate,
      },
    });

    if (existingSummary) {
      throw new Error(`A ${data.type.toLowerCase()} summary already exists for this date range`);
    }

    // Create the summary
    const summary = await prisma.summary.create({
      data: {
        type: data.type,
        startDate: data.startDate,
        endDate: data.endDate,
        totalOrders: data.totalOrders,
        totalRevenue: data.totalRevenue,
        totalDeliveryCharges: data.totalDeliveryCharges,
        totalProfit: data.totalProfit,
        notes: data.notes,
        isFinalized: true,
        generatedById: userId,
      },
      include: {
        generatedBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    // If order IDs are provided, update those orders to link to this summary
    if (data.orderIds && data.orderIds.length > 0) {
      await prisma.order.updateMany({
        where: {
          id: {
            in: data.orderIds,
          },
        },
        data: {
          summaryId: summary.id,
        },
      });
    }

    // Revalidate the summaries page
    revalidatePath('/dashboard/summaries');

    return summary;
  } catch (error) {
    console.error('Failed to create summary:', error);
    if (error instanceof Error) {
      throw error;
    }
    throw new Error('Failed to create summary');
  }
}

/**
 * Get a specific summary by ID with all related data
 */
export async function getSummaryById(id: string): Promise<SummaryWithDetails | null> {
  try {
    const prisma = getPrisma();

    const summary = await prisma.summary.findUnique({
      where: { id },
      include: {
        generatedBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        orders: {
          include: {
            buyer: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            orderItems: {
              include: {
                product: {
                  select: {
                    id: true,
                    name: true,
                    sourcePrice: true,
                  },
                },
              },
            },
          },
        },
        childSummaries: {
          include: {
            generatedBy: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        },
      },
    });

    return summary;
  } catch (error) {
    console.error('Failed to fetch summary by ID:', error);
    throw new Error('Failed to fetch summary details');
  }
}

/**
 * Calculate profit for orders (helper function)
 */
export async function calculateOrderProfit(order: OrderWithDetails): Promise<number> {
  return order.orderItems.reduce((total, item) => {
    const itemProfit = (item.price - item.sourcePrice) * item.quantity;
    return total + itemProfit;
  }, 0);
}
"use server";

import { getPrisma } from "@udoy/utils/db-utils";
import { CookieUtil } from "@udoy/utils/cookie-util";
import { Summary, SummaryType, Order, OrderStatus, Role } from "@prisma/client";
import { revalidatePath } from "next/cache";
import { ActionError } from "@udoy/utils/app-error";

// Type definitions for return values
export type SummaryWithCreator = Summary & {
  generatedBy: {
    id: number;
    name: string;
    email: string;
  };
};

export type OrderWithDetails = Order & {
  buyer: {
    id: number;
    name: string;
    email: string;
  };
  orderItems: Array<{
    id: number;
    productId: string;
    quantity: number;
    price: number;
    sourcePrice: number;
    product: {
      id: string;
      name: string;
      sourcePrice: number;
    };
  }>;
};

export type SummaryWithDetails = Summary & {
  generatedBy: {
    id: number;
    name: string;
    email: string;
  };
  orders: OrderWithDetails[];
  childSummaries: SummaryWithCreator[];
};

/**
 * Fetch all summaries with creator information
 */
export async function getSummaries(type?: SummaryType) {
  try {
    const userId = await CookieUtil.userId();
    if (!userId) {
      return ActionError("Login to Continue");
    }

    const prisma = getPrisma();

    const admin = await prisma.user.findUnique({
      where: {
        id: userId,
        role: {
          in: [Role.ADMIN, Role.SUPER_ADMIN, Role.MAINTAINER],
        },
      },
    });

    if (!admin) {
      return ActionError("Unauthorized");
    }

    const whereClause = type ? { type } : {};

    const summaries = await prisma.summary.findMany({
      where: whereClause,
      include: {
        generatedBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return summaries;
  } catch (error) {
    console.error('Failed to fetch summaries:', error);
    return ActionError('Failed to fetch summaries');
  }
}

/**
 * Fetch orders within a specific date range for summary creation
 */
export async function getOrdersForDateRange(
  startDate: Date,
  endDate: Date
) {
  try {
    const userId = await CookieUtil.userId();
    if (!userId) {
      return ActionError("Login to Continue");
    }

    const prisma = getPrisma();

    const admin = await prisma.user.findUnique({
      where: {
        id: userId,
        role: {
          in: [Role.ADMIN, Role.SUPER_ADMIN, Role.MAINTAINER],
        },
      },
    });

    if (!admin) {
      return ActionError("Unauthorized");
    }

    const orders = await prisma.order.findMany({
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
        status: {
          in: [OrderStatus.DELIVERED, OrderStatus.CONFIRMED, OrderStatus.PACKED, OrderStatus.SHIPPING],
        },
      },
      include: {
        buyer: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        orderItems: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                sourcePrice: true,
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Calculate profit for each order if not already set
    // const ordersWithProfit = orders.map(order => {
    //   if (order.profit === 0) {
    //     const calculatedProfit = order.orderItems.reduce((total, item) => {
    //       const itemProfit = (item.price - item.sourcePrice) * item.quantity;
    //       return total + itemProfit;
    //     }, 0);
    //     return { ...order, profit: calculatedProfit };
    //   }
    //   return order;
    // });

    return orders;
  } catch (error) {
    console.error('Failed to fetch orders for date range:', error);
    return ActionError('Failed to fetch orders for the specified date range');
  }
}

/**
 * Fetch child summaries for a given date range (used for weekly/monthly/yearly summaries)
 */
export async function getChildSummariesForDateRange(
  type: SummaryType,
  startDate: Date,
  endDate: Date
) {
  try {
    const userId = await CookieUtil.userId();
    if (!userId) {
      return ActionError("Login to Continue");
    }

    const prisma = getPrisma();

    const admin = await prisma.user.findUnique({
      where: {
        id: userId,
        role: {
          in: [Role.ADMIN, Role.SUPER_ADMIN, Role.MAINTAINER],
        },
      },
    });

    if (!admin) {
      return ActionError("Unauthorized");
    }

    // Determine the child summary type based on parent type
    let childType: SummaryType;
    switch (type) {
      case SummaryType.WEEKLY:
        childType = SummaryType.DAILY;
        break;
      case SummaryType.MONTHLY:
        childType = SummaryType.WEEKLY;
        break;
      case SummaryType.YEARLY:
        childType = SummaryType.MONTHLY;
        break;
      default:
        return []; // Daily summaries don't have child summaries
    }

    const childSummaries = await prisma.summary.findMany({
      where: {
        type: childType,
        startDate: {
          gte: startDate,
        },
        endDate: {
          lte: endDate,
        },
        isFinalized: true,
      },
      include: {
        generatedBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        startDate: 'asc',
      },
    });

    return childSummaries;
  } catch (error) {
    console.error('Failed to fetch child summaries:', error);
    return ActionError('Failed to fetch child summaries for the specified date range');
  }
}

/**
 * Create a new summary with the provided data
 */
export async function createSummary(data: {
  type: SummaryType;
  startDate: Date;
  endDate: Date;
  totalOrders: number;
  totalRevenue: number;
  totalDeliveryCharges: number;
  totalProfit: number;
  notes?: string | null;
  orderIds?: number[];
}) {
  try {
    const userId = await CookieUtil.userId();
    if (!userId) {
      return ActionError("Login to Continue");
    }

    const prisma = getPrisma();

    const admin = await prisma.user.findUnique({
      where: {
        id: userId,
        role: {
          in: [Role.ADMIN, Role.SUPER_ADMIN, Role.MAINTAINER],
        },
      },
    });

    if (!admin) {
      return ActionError("Unauthorized");
    }

    // Check if a summary already exists for this type and date range
    const existingSummary = await prisma.summary.findFirst({
      where: {
        type: data.type,
        startDate: data.startDate,
        endDate: data.endDate,
      },
    });

    if (existingSummary) {
      return ActionError(`A ${data.type.toLowerCase()} summary already exists for this date range`);
    }

    // Create the summary
    const summary = await prisma.summary.create({
      data: {
        type: data.type,
        startDate: data.startDate,
        endDate: data.endDate,
        totalOrders: data.totalOrders,
        totalRevenue: data.totalRevenue,
        totalDeliveryCharges: data.totalDeliveryCharges,
        totalProfit: data.totalProfit,
        notes: data.notes,
        isFinalized: true,
        generatedById: userId,
      },
      include: {
        generatedBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    // If order IDs are provided, update those orders to link to this summary
    if (data.orderIds && data.orderIds.length > 0) {
      await prisma.order.updateMany({
        where: {
          id: {
            in: data.orderIds,
          },
        },
        data: {
          summaryId: summary.id,
        },
      });
    }

    // Revalidate the summaries page
    revalidatePath('/dashboard/summaries');

    return summary;
  } catch (error) {
    console.error('Failed to create summary:', error);
    return ActionError('Failed to create summary');
  }
}

/**
 * Get a specific summary by ID with all related data
 */
export async function getSummaryById(id: string) {
  try {
    const userId = await CookieUtil.userId();
    if (!userId) {
      return ActionError("Login to Continue");
    }

    const prisma = getPrisma();

    const admin = await prisma.user.findUnique({
      where: {
        id: userId,
        role: {
          in: [Role.ADMIN, Role.SUPER_ADMIN, Role.MAINTAINER],
        },
      },
    });

    if (!admin) {
      return ActionError("Unauthorized");
    }

    const summary = await prisma.summary.findUnique({
      where: { id },
      include: {
        generatedBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        orders: {
          include: {
            buyer: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            orderItems: {
              include: {
                product: {
                  select: {
                    id: true,
                    name: true,
                    sourcePrice: true,
                  },
                },
              },
            },
          },
        },
        childSummaries: {
          include: {
            generatedBy: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        },
      },
    });

    return summary;
  } catch (error) {
    console.error('Failed to fetch summary by ID:', error);
    return ActionError('Failed to fetch summary details');
  }
}

/**
 * Update order profit in the database
 */
export async function updateOrderProfit(orderId: number, newProfit: number) {
  try {
    const userId = await CookieUtil.userId();
    if (!userId) {
      return ActionError("Login to Continue");
    }

    const prisma = getPrisma();

    const admin = await prisma.user.findUnique({
      where: {
        id: userId,
        role: {
          in: [Role.ADMIN, Role.SUPER_ADMIN, Role.MAINTAINER],
        },
      },
    });

    if (!admin) {
      return ActionError("Unauthorized");
    }

    // Validate the new profit value
    if (isNaN(newProfit) || newProfit < 0) {
      return ActionError("Invalid profit value");
    }

    // Update the order profit
    const updatedOrder = await prisma.order.update({
      where: { id: orderId },
      data: { profit: newProfit },
      include: {
        buyer: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        orderItems: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                sourcePrice: true,
              },
            },
          },
        },
      },
    });

    // Revalidate the summaries page
    revalidatePath('/dashboard/summaries');

    return updatedOrder;
  } catch (error) {
    console.error('Failed to update order profit:', error);
    return ActionError('Failed to update order profit');
  }
}

/**
 * Calculate profit for orders (helper function)
 */
export async function calculateOrderProfit(order: OrderWithDetails): Promise<number> {
  return order.orderItems.reduce((total, item) => {
    const itemProfit = (item.price - item.sourcePrice) * item.quantity;
    return total + itemProfit;
  }, 0);
}
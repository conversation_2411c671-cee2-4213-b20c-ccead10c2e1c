import { getPrisma } from "@udoy/utils/db-utils";
import { searchMeiliProducts } from "../meilisearch";

export async function getProducts(query: string, isAdmin: boolean = false) {
  const prisma = getPrisma();
  const hits = await searchMeiliProducts(query);
  const productIds = hits.map((hit) => hit.id);

  const q = {
    id: {
      in: productIds,
    },
  } as any;

  if (!isAdmin) {
    q["hide"] = false;
    q["category"] = {
      hide: false,
    };
  }

  const unorderedProducts = await prisma.product.findMany({
    where: q,
    include: {
      images: true,
      unit: true,
      category: { select: { slug: true } },
      availability: true,
    },
  });

  const productMap = new Map<string, (typeof unorderedProducts)[0]>();
  for (const product of unorderedProducts) {
    productMap.set(product.id, product);
  }

  // Use the original `productIds` array (which is correctly ordered) to build the final, sorted list
  const orderedProducts = productIds
    .map((id) => productMap.get(id))
    .filter((p): p is NonNullable<typeof p> => !!p); // Filter out any potential misses

  return orderedProducts;
}
